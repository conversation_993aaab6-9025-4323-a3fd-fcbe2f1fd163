import os
import dotenv
import re
import json
from django.utils import timezone
from django.http import JsonResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt

from oauth2_provider.models import AccessToken
from firebase_admin import firestore, credentials, auth
import firebase_admin

dotenv.load_dotenv()

# --- Firebase Initialization ---
try:
    if not firebase_admin._apps:
        # Try to get service account from environment variable (file path)
        service_account_path = os.getenv('FIREBASE_SERVICE_ACCOUNT_KEY_PATH')
        
        if service_account_path and os.path.exists(service_account_path):
            # Use file path if provided and exists
            cred = credentials.Certificate(service_account_path)
        else:
            # Fallback to firebase_app.json in project root
            current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            firebase_json_path = os.path.join(current_dir, 'firebase_app.json')
            
            if os.path.exists(firebase_json_path):
                cred = credentials.Certificate(firebase_json_path)
            else:
                raise ValueError("Firebase service account key not found. Set FIREBASE_SERVICE_ACCOUNT_KEY_PATH or ensure firebase_app.json exists in project root.")
        
        firebase_admin.initialize_app(cred)
        print("Firebase initialized successfully")
except Exception as e:
    print(f"Failed to initialize Firebase: {e}")

def get_firestore_db():
    return firestore.client()

def _create_user_in_firestore(user, normalized_phone):
    """Create a user document in Firestore after Firebase Auth user creation."""
    try:
        db = get_firestore_db()
        user_data = {
            "createdAt": timezone.now().isoformat(),
            "displayName": user.display_name or "Customer",
            "email": "",  # Empty email as shown in screenshot
            "isProfileComplete": True,
            "phoneNumber": user.phone_number,
            "uid": user.uid,
            "updatedAt": timezone.now().isoformat()
        }
        # Create nested structure: users/{userId}/profile/data
        db.collection("users").document(user.uid).collection("profile").document("data").set(user_data)
        print(f"User document created in Firestore at users/{user.uid}/profile/data for phone: {normalized_phone}")
    except Exception as firestore_error:
        print(f"Warning: Failed to create user in Firestore: {firestore_error}")
        # Don't fail the entire request if Firestore write fails

# --- Helper Functions ---
def _normalize_phone_number(phone_number: str) -> str:
    """Cleans and standardizes a phone number to E.164 format."""
    cleaned = re.sub(r'[^\d+]', '', phone_number)
    if not cleaned.startswith('+'):
        if len(cleaned) == 10:
            cleaned = '+91' + cleaned
        elif len(cleaned) == 11 and cleaned.startswith('0'):
            cleaned = '+91' + cleaned[1:]
        else:
            cleaned = '+' + cleaned
    return cleaned

# --- Base Authenticated View ---
@method_decorator(csrf_exempt, name='dispatch')
class AuthenticatedView(View):
    """
    A base view that handles OAuth2 token authentication.
    """
    def dispatch(self, request, *args, **kwargs):
        token = request.headers.get('Authorization', '').replace('Bearer ', '')
        if not token:
            # Fallback for testing or simple clients
            token = request.GET.get('token') or request.POST.get('token')

        is_valid, error = self._authenticate_token(token)
        if not is_valid:
            return JsonResponse({'error': error}, status=401)
        
        # Add user to request if you need it later
        # request.user = AccessToken.objects.get(token=token).user
        
        return super().dispatch(request, *args, **kwargs)

    def _authenticate_token(self, token: str):
        if not token:
            return False, "No token provided"
        try:
            access_token = AccessToken.objects.get(token=token)
            if access_token.is_valid() and access_token.expires > timezone.now():
                return True, None
            return False, "Token expired or invalid"
        except AccessToken.DoesNotExist:
            return False, "Unauthorized"
        except Exception as e:
            return False, str(e)

# --- API Views ---
class CheckTokenView(AuthenticatedView):
    """
    Validate an access token via API. Inherits authentication.
    """
    def get(self, request):
        return JsonResponse({'valid': True})

    def post(self, request):
        return JsonResponse({'valid': True})

class CheckNumberView(AuthenticatedView):
    """
    Checks if a given phone number exists as a document ID in Firestore.
    """
    def get(self, request):
        number = request.GET.get('number')
        if not number:
            return JsonResponse({"error": "Number parameter is required"}, status=400)

        number_str = str(number).strip()
        if not number_str.startswith('+'):
            number_str = '+' + number_str

        try:
            db = get_firestore_db()
            doc_ref = db.collection("pos_users").document(number_str)
            return JsonResponse({"number_exists": doc_ref.get().exists})
        except Exception as e:
            return JsonResponse({"error": f"Error checking number in Firestore: {e}"}, status=500)


class UserCreateView(AuthenticatedView):
    """
    Searches for a user in Firebase Auth by phone number.
    """
    def get(self, request):
        print(f"GET request received with query params: {request.GET}")
        phone_number = request.GET.get('phone_number')
        if not phone_number:
            return JsonResponse({"error": "phone_number parameter is required"}, status=400)

        normalized_phone = _normalize_phone_number(phone_number)

        try:
            user = auth.get_user_by_phone_number(normalized_phone)
            # take the user.id and search in firestore
            db = get_firestore_db()
            # path is user/uuid/profile/data > displayName and email
            doc_ref = db.collection("users").document(user.uid).collection("profile").document("data")
            doc = doc_ref.get()

            if doc.exists:
                doc_data = doc.to_dict()
                # Access specific fields
                display_name = doc_data.get("displayName", "Customer")
                email = doc_data.get("email", "Customer")
                phone_number = doc_data.get("phoneNumber", "Customer")
                return JsonResponse({
                    "success": True,
                    "customer_id": user.uid,
                    "customer_name": display_name,
                    "phone_number": phone_number,
                    "email": email,
                })
            else:
                return JsonResponse({"success": False, "error": "User not found"}, status=404)
        except auth.UserNotFoundError:
            return JsonResponse({"success": False, "error": "User not found"}, status=404)
        except Exception as e:
            return JsonResponse({"success": False, "error": str(e)}, status=500)
    """
    Creates a new user in Firebase Auth if they don't already exist.
    """
    def post(self, request):
        print(f"POST request received with body: {request.body}")
        try:
            # Check if request body is empty
            if not request.body:
                return JsonResponse({"error": "Request body is empty. Please provide JSON data."}, status=400)

            data = json.loads(request.body)
            phone_number = data.get('phone_number')
            username = data.get('username')
            if not phone_number:
                return JsonResponse({"error": "phone_number is required"}, status=400)
        except json.JSONDecodeError:
            return JsonResponse({"error": "Invalid JSON. Please provide valid JSON data in request body."}, status=400)

        normalized_phone = _normalize_phone_number(phone_number)

        try:
            # Check if user exists first
            try:
                user = auth.get_user_by_phone_number(normalized_phone)
                return JsonResponse({
                    "success": True,
                    "created": False,
                    "customer_id": user.uid,
                    "customer_name": user.display_name or "Customer",
                    "phone_number": user.phone_number,
                })
            except auth.UserNotFoundError:
                # User does not exist, so create them
                new_user = auth.create_user(
                    phone_number=normalized_phone,
                    display_name=username or "Customer"
                )
                
                # Also create user document in Firestore
                _create_user_in_firestore(new_user, normalized_phone)
                
                return JsonResponse({
                    "success": True,
                    "created": True,
                    "customer_id": new_user.uid,
                    "customer_name": new_user.display_name or "Customer",
                    "phone_number": new_user.phone_number,
                }, status=201)
        except Exception as e:
            return JsonResponse({"success": False, "error": str(e)}, status=500)
class UserDeleteView(AuthenticatedView):
    """
    Delete a user from both Firebase Auth and Firestore using OAuth token.
    No payload needed - user ID extracted from token.
    """
    def delete(self, request):
        try:
            # Get phone number from query parameter (much simpler than long Firebase UID)
            phone_number = request.GET.get('phone_number')
            if not phone_number:
                return JsonResponse({"error": "phone_number parameter is required"}, status=400)
            # Normalize the phone number
            normalized_phone = _normalize_phone_number(phone_number)
            # Get Firebase user by phone number
            firebase_user = auth.get_user_by_phone_number(normalized_phone)
            firebase_uid = firebase_user.uid
            # Delete from Firebase Authentication
            auth.delete_user(firebase_uid)
            # Delete from Firestore
            _delete_user_from_firestore(firebase_uid)
            return JsonResponse({
                "success": True,
                "message": "User deleted successfully from both Firebase Auth and Firestore",
                "deleted_uid": firebase_uid,
                "phone_number": normalized_phone
            })
        except auth.UserNotFoundError:
            return JsonResponse({"success": False, "error": "User not found in Firebase Auth"}, status=404)
        except Exception as e:
            return JsonResponse({"success": False, "error": str(e)}, status=500)
class VerifyPosUserView(AuthenticatedView):
    """
    Verify POS user by phone number in Firestore `pos_users`.
    Accepts query param `phone_number` or `number`.
    """

    def get(self, request):
        phone_number = request.GET.get("phone_number") or request.GET.get("number")
        if not phone_number:
            return JsonResponse({"success": False, "error": "phone_number or number parameter is required"}, status=400)

        normalized_phone = _normalize_phone_number(phone_number)

        try:
            db = get_firestore_db()
            doc_ref = db.collection("pos_users").document(normalized_phone)
            doc = doc_ref.get()

            if doc.exists:
                data = doc.to_dict()
                return JsonResponse({
                    "success": True,
                    "name": data.get("displayName", "")
                })
            else:
                return JsonResponse({"success": False})
        except Exception as e:
            return JsonResponse({"success": False, "error": str(e)}, status=500)
