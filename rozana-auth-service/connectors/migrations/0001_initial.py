# Generated by Django 5.2.5 on 2025-09-25 07:48

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='GupshupConfig',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(help_text='A name to identify this configuration', max_length=100, unique=True)),
                ('userid', models.<PERSON>r<PERSON><PERSON>(help_text='Gupshup API userid', max_length=100)),
                ('password', models.Char<PERSON>ield(help_text='Gupshup API password', max_length=255)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this configuration is active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Gupshup Configuration',
                'verbose_name_plural': 'Gupshup Configurations',
                'db_table': 'GUPSHUP_CONFIG',
                'ordering': ['-is_active', 'name'],
            },
        ),
    ]
