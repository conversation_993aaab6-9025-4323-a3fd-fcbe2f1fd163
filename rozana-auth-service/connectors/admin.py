from django.contrib import admin
from .models import GupshupConfig


@admin.register(GupshupConfig)
class GupshupConfigAdmin(admin.ModelAdmin):
    """
    Admin interface for managing Gupshup OTP configurations
    """
    list_display = ('name', 'userid', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active', 'created_at', 'updated_at')
    search_fields = ('name', 'userid')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Configuration', {
            'fields': ('name', 'userid', 'password', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_readonly_fields(self, request, obj=None):
        # Make password read-only in the change form
        if obj:  # Editing an existing object
            return self.readonly_fields + ('password',)
        return self.readonly_fields
