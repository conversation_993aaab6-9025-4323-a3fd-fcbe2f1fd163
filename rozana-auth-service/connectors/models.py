from django.db import models
import uuid


class GupshupConfig(models.Model):
    """
    Simple model to store Gupshup API credentials.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True, help_text="A name to identify this configuration")
    userid = models.CharField(max_length=100, help_text="Gupshup API userid")
    password = models.CharField(max_length=255, help_text="Gupshup API password")
    is_active = models.Bo<PERSON>anField(default=True, help_text="Whether this configuration is active")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "GUPSHUP_CONFIG"
        verbose_name = "Gupshup Configuration"
        verbose_name_plural = "Gupshup Configurations"
        ordering = ['-is_active', 'name']

    def __str__(self):
        return f"{self.name} ({'Active' if self.is_active else 'Inactive'})"

