import random
import logging
import requests
from urllib.parse import urlencode
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)

MOBILENXT_ACCESS_KEY = getattr(settings, 'MOBILENXT_ACCESS_KEY')
MOBILENXT_TID = getattr(settings, 'MO<PERSON>LENXT_TID')
MOBILENXT_OTP_LENGTH = int(getattr(settings, 'MOBILENXT_OTP_LENGTH', 4))

class MobileNXTOTPService:
    """
    Service to handle OTP operations using MobileNXT API.
    Uses Redis for OTP storage and validation.
    """

    BASE_URL = "https://api.mobilnxt.in/api/push"
    CACHE_PREFIX = "otp_"
    OTP_LENGTH = MOBILENXT_OTP_LENGTH
    OTP_EXPIRY = 300  # 5 minutes in seconds

    def __init__(self, sender_id='RZAN<PERSON>'):
        """
        Initialize with MobileNXT credentials.
        Args:
            sender_id: Sender ID to be displayed (default: 'R<PERSON><PERSON><PERSON>')
        """
        self.access_key = MO<PERSON><PERSON>NXT_ACCESS_KEY
        self.sender_id = sender_id
        self.tid = MOBILENXT_TID

    def generate_otp(self):
        """Generate a random numeric OTP."""
        return str(random.randint(10 ** (self.OTP_LENGTH - 1), (10 ** self.OTP_LENGTH) - 1))

    def get_cache_key(self, phone_number):
        """Get Redis cache key for OTP storage."""
        return f"{self.CACHE_PREFIX}{phone_number}"

    def send_otp(self, phone_number):
        """
        Send OTP to the given phone number using MobileNXT API.
        Args:
            phone_number: Recipient's phone number (10 digits)
        Returns:
            dict: {'status': bool, 'message': str}
        """
        try:
            # Generate new OTP
            otp = self.generate_otp()

            # Prepare message
            message = f"Your OTP for rozana.in is {otp}"

            # Prepare request data
            data = {
                'accesskey': self.access_key,
                'tid': self.tid,
                'to': phone_number,
                'text': message,
                'from': self.sender_id,
                'unicode': '0'
            }

            # Make API request
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
            response = requests.post(
                self.BASE_URL,
                headers=headers,
                data=urlencode(data)
            )

            # Check response
            if response.status_code == 200:
                # Store OTP in Redis with expiry
                cache_key = self.get_cache_key(phone_number)
                cache.set(cache_key, otp, self.OTP_EXPIRY)

                logger.info(f"OTP sent to {phone_number}")
                return {
                    'status': True,
                    'message': 'OTP sent successfully'
                }
            else:
                logger.error(f"Failed to send OTP. Status: {response.status_code}, Response: {response.text}")
                return {
                    'status': False,
                    'message': 'Failed to send OTP',
                    'error': response.text
                }

        except Exception as e:
            logger.exception(f"Error in send_otp: {str(e)}")
            return {
                'status': False,
                'message': 'Failed to send OTP',
                'error': str(e)
            }

    def verify_otp(self, phone_number, otp_code):
        """
        Verify the OTP for the given phone number.
        Args:
            phone_number: User's phone number
            otp_code: OTP to verify
        Returns:
            dict: {'status': bool, 'message': str}
        """
        try:
            cache_key = self.get_cache_key(phone_number)
            stored_otp = cache.get(cache_key)

            if not stored_otp:
                return {
                    'status': False,
                    'message': 'OTP expired or not found'
                }

            if int(stored_otp) == int(otp_code):
                # Clear OTP after successful verification
                cache.delete(cache_key)
                return {
                    'status': True,
                    'message': 'OTP verified successfully'
                }

            else:
                return {
                    'status': False,
                    'message': 'Invalid OTP'
                }
        except Exception as e:
            logger.exception(f"Error in verify_otp: {str(e)}")
            return {
                'status': False,
                'message': 'Error verifying OTP',
                'error': str(e)
            }
