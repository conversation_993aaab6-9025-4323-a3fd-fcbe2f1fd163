import requests
from urllib.parse import quote
import logging

logger = logging.getLogger(__name__)

class GupshupOTPError(Exception):
    """Custom exception for Gupshup OTP related errors."""
    pass

class GupshupOTPService:
    """
    Simple service to handle Gupshup OTP operations.
    Follows the exact API specification from the documentation.
    """

    BASE_URL = "https://enterprise.smsgupshup.com/GatewayAPI/rest"
    OTP_LENGTH = 6  # Default OTP length as per documentation
    OTP_TYPE = "NUMERIC"  # Default OTP type as per documentation

    def __init__(self, config):
        """
        Initialize with GupshupConfig instance.
        Args:
            config: GupshupConfig model instance with userid and password
        """
        self.config = config

    def build_url(self, phone_number, otp_code=None, message=None):
        """
        Build the API URL for Gupshup API calls.
        """
        params = {
            'method': 'TWO_FACTOR_AUTH',
            'v': '1.1',
            'userid': self.config.userid,
            'password': self.config.password,
            'phone_no': phone_number,
            'format': 'text',  # Always use text format as per documentation
        }

        if otp_code:
            # For OTP verification
            params['otp_code'] = otp_code
        else:
            # For OTP generation
            params['msg'] = message or f"Your OTP code is %code%"
            params['otpCodeLength'] = str(self.OTP_LENGTH)
            params['otpCodeType'] = self.OTP_TYPE

        # Build query string
        query_str = '&'.join(f"{k}={quote(str(v))}" for k, v in params.items())
        return f"{self.BASE_URL}?{query_str}"

    def parse_response(self, response_text):
        """
        Parse the API response in text format.
        Format: "status | phone_no | id | message"
        """
        try:
            parts = [p.strip() for p in response_text.split('|')]
            if len(parts) >= 3:
                return {
                    'status': parts[0].lower() == 'success',
                    'phone_no': parts[1],
                    'id': parts[2],
                    'message': parts[3] if len(parts) > 3 else ''
                }
            return {'status': False, 'message': 'Invalid response format'}
        except Exception as e:
            logger.error(f"Failed to parse response: {str(e)}")
            return {'status': False, 'message': 'Failed to parse response'}

    def send_otp(self, phone_number, message=None):
        """
        Send OTP to the specified phone number.
        
        Args:
            phone_number: Phone number with country code (e.g., '919876543210')
            message: Optional custom message template with %code% placeholder
            
        Returns:
            dict: {
                'success': bool,
                'message': str,
                'phone_no': str,
                'id': str
            }
        """
        try:
            # Build and send request
            url = self.build_url(phone_number, message=message)
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            # Parse and return response
            result = self.parse_response(response.text)
            if result['status']:
                logger.info(f"OTP sent to {phone_number}. ID: {result.get('id')}")
            else:
                logger.error(f"Failed to send OTP: {result.get('message')}")
            return result
            
        except requests.RequestException as e:
            logger.error(f"Request failed: {str(e)}")
            return {
                'success': False,
                'message': 'Failed to connect to OTP service'
            }
        except Exception as e:
            logger.error(f"Error sending OTP: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': 'An error occurred while sending OTP'
            }

    def verify_otp(self, phone_number, otp_code):
        """
        Verify the provided OTP for the given phone number.
        
        Args:
            phone_number: Phone number with country code
            otp_code: The OTP code to verify
            
        Returns:
            dict: {
                'success': bool,
                'message': str,
                'phone_no': str,
                'id': str
            }
        """
        try:
            # Basic validation
            if not otp_code or not str(otp_code).isdigit():
                return {
                    'success': False,
                    'message': 'Invalid OTP format'
                }

            # Build and send verification request
            url = self.build_url(phone_number, otp_code=otp_code)
            response = requests.get(url, timeout=10)
            response.raise_for_status()

            # Parse and return response
            result = self.parse_response(response.text)
            if result['status']:
                logger.info(f"OTP verified for {phone_number}")
            else:
                logger.warning(f"OTP verification failed: {result.get('message')}")
            return result

        except requests.RequestException as e:
            logger.error(f"Request failed: {str(e)}")
            return {
                'success': False,
                'message': 'Failed to connect to OTP service'
            }
        except Exception as e:
            logger.error(f"Error verifying OTP: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': 'An error occurred while verifying OTP'
            }