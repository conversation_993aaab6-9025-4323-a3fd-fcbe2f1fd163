import json
import logging
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.conf import settings
from connectors.services.mobilenxt_otp import MobileNXTOTPService
from auth_service.views import AuthenticatedView

logger = logging.getLogger(__name__)

class SendOTPView(AuthenticatedView):
    """
    API View to send OTP to a phone number using Gupshup service
    Requires authentication
    """
    @method_decorator(csrf_exempt)
    def post(self, request, *args, **kwargs):
        try:
            # Try to parse JSON data if Content-Type is application/json
            if request.content_type == 'application/json':
                try:
                    data = json.loads(request.body)
                    phone_number = data.get('phone_number')
                except json.JSONDecodeError:
                    return JsonResponse({"success": False, "error": "Invalid JSON data"}, status=400)
            else:
                phone_number = request.POST.get('phone_number')

            if not phone_number:
                return JsonResponse({"success": False, "error": "Phone number is required"}, status=400)

            # Send OTP with %code% placeholder as required by Gupshup
            otp_service = MobileNXTOTPService()
            result = otp_service.send_otp(phone_number)

            if result.get('status'):
                return JsonResponse({"success": True, "message": "OTP sent successfully", "phone_number": phone_number})
            else:
                return JsonResponse({"success": False, "error": result.get('message', 'Failed to send OTP')}, status=400)

        except Exception as e:
            logger.error(f"Error sending OTP: {str(e)}", exc_info=True)
            return JsonResponse({"success": False, "error": "Failed to send OTP"}, status=500)


class VerifyOTPView(AuthenticatedView):
    """
    API View to verify OTP using Gupshup service
    Requires authentication
    """
    @method_decorator(csrf_exempt)
    def post(self, request, *args, **kwargs):
        try:
            # Try to parse JSON data if Content-Type is application/json
            if request.content_type == 'application/json':
                try:
                    data = json.loads(request.body)
                    phone_number = data.get('phone_number')
                    otp_code = data.get('otp_code')
                except json.JSONDecodeError:
                    return JsonResponse({"success": False, "error": "Invalid JSON data"}, status=400)
            else:
                # Fall back to form data
                phone_number = request.POST.get('phone_number')
                otp_code = request.POST.get('otp_code')

            if not all([phone_number, otp_code]):
                return JsonResponse({"success": False, "error": "Phone number and OTP code are required"}, status=400)

            # Verify OTP
            otp_service = MobileNXTOTPService()
            result = otp_service.verify_otp(phone_number, otp_code)

            if result.get('status'):
                return JsonResponse({"success": True, "message": "OTP verified successfully", "phone_number": phone_number})
            else:
                return JsonResponse({"success": False, "error": result.get('id', 'Invalid OTP')}, status=400)
                
        except Exception as e:
            logger.error(f"Error verifying OTP: {str(e)}", exc_info=True)
            return JsonResponse({"success": False, "error": "Failed to verify OTP"}, status=500)